"""
Example implementation of multi-threaded email processing
This demonstrates how to implement concurrent file processing with thread-safe OCR
"""

import os
import threading
import time
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from queue import Queue
from typing import List, Dict, Any
from PyQt6.QtCore import QThread, pyqtSignal

# Import existing components
from email_processor import EmailParser
from paddle_ocr_service import PaddleOcrService


class ThreadLocalOCR:
    """Thread-local OCR service manager to ensure each thread has its own OCR instance"""
    
    def __init__(self):
        self._local = threading.local()
        self._lock = threading.Lock()
        self._initialized_threads = set()
    
    def get_ocr_service(self) -> PaddleOcrService:
        """Get OCR service for current thread, creating if necessary"""
        thread_id = threading.get_ident()
        
        if not hasattr(self._local, 'ocr_service'):
            with self._lock:
                if thread_id not in self._initialized_threads:
                    print(f"Initializing OCR for thread {thread_id}")
                    self._local.ocr_service = PaddleOcrService()
                    self._initialized_threads.add(thread_id)
        
        return self._local.ocr_service
    
    def cleanup_thread(self):
        """Clean up OCR service for current thread"""
        thread_id = threading.get_ident()
        if hasattr(self._local, 'ocr_service'):
            # OCR service cleanup would go here if needed
            delattr(self._local, 'ocr_service')
            with self._lock:
                self._initialized_threads.discard(thread_id)


class ThreadSafeEmailParser(EmailParser):
    """Thread-safe version of EmailParser with thread-local OCR"""
    
    def __init__(self, thread_local_ocr: ThreadLocalOCR):
        super().__init__()
        self.thread_local_ocr = thread_local_ocr
        # Override the OCR service with thread-local version
        self.ocr_service = None  # Will be set per-thread
    
    def _ensure_thread_local_ocr(self):
        """Ensure current thread has its OCR service set"""
        if self.ocr_service is None:
            self.ocr_service = self.thread_local_ocr.get_ocr_service()
    
    def parse_eml_file(self, file_path):
        """Parse email file with thread-local OCR"""
        # Ensure we have thread-local OCR
        self._ensure_thread_local_ocr()
        
        # Call parent implementation
        return super().parse_eml_file(file_path)


class ThreadedProcessingThread(QThread):
    """Multi-threaded email processing with progress reporting"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    file_processed = pyqtSignal(str, dict)
    finished_processing = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, file_paths: List[str], output_path: str, max_workers: int = None):
        super().__init__()
        self.file_paths = file_paths
        self.output_path = output_path
        self.max_workers = max_workers or min(4, os.cpu_count())
        self.results = []
        self.processed_count = 0
        self.total_files = len(file_paths)
        self.results_lock = threading.Lock()
        
        # Thread-local OCR manager
        self.thread_local_ocr = ThreadLocalOCR()
        
        print(f"Initialized threaded processor with {self.max_workers} workers for {self.total_files} files")
    
    def process_single_file(self, file_path: str) -> Dict[str, Any]:
        """Process a single email file in worker thread"""
        try:
            # Create thread-safe parser for this thread
            parser = ThreadSafeEmailParser(self.thread_local_ocr)
            
            # Process the file
            result = parser.parse_eml_file(file_path)
            
            # Thread-safe progress update
            with self.results_lock:
                self.processed_count += 1
                progress = int((self.processed_count / self.total_files) * 100)
                
                # Emit signals (these are thread-safe in PyQt6)
                self.progress_updated.emit(progress)
                self.status_updated.emit(f"Processed: {os.path.basename(file_path)} ({self.processed_count}/{self.total_files})")
                self.file_processed.emit(file_path, result)
            
            return result
            
        except Exception as e:
            error_msg = f"Error processing {file_path}: {str(e)}"
            self.error_occurred.emit(error_msg)
            return {
                'filename': os.path.basename(file_path),
                'file_path': str(file_path),
                'error': error_msg
            }
        finally:
            # Clean up thread-local resources
            self.thread_local_ocr.cleanup_thread()
    
    def run(self):
        """Main processing loop with thread pool"""
        try:
            self.status_updated.emit(f"Starting threaded processing with {self.max_workers} workers...")
            start_time = time.time()
            
            # Use ThreadPoolExecutor for concurrent processing
            with ThreadPoolExecutor(max_workers=self.max_workers, thread_name_prefix="EmailWorker") as executor:
                # Submit all files for processing
                future_to_file = {
                    executor.submit(self.process_single_file, file_path): file_path 
                    for file_path in self.file_paths
                }
                
                # Collect results as they complete
                for future in as_completed(future_to_file):
                    if self.isInterruptionRequested():
                        # Cancel remaining futures
                        for f in future_to_file:
                            f.cancel()
                        break
                    
                    file_path = future_to_file[future]
                    try:
                        result = future.result()
                        self.results.append(result)
                    except Exception as e:
                        error_msg = f"Future error for {file_path}: {str(e)}"
                        self.error_occurred.emit(error_msg)
            
            if not self.isInterruptionRequested():
                # Export to Excel
                self.status_updated.emit("Exporting to Excel...")
                self.export_to_excel()
                
                elapsed_time = time.time() - start_time
                self.status_updated.emit(f"Processing completed in {elapsed_time:.1f} seconds!")
                self.finished_processing.emit(self.results)
            
        except Exception as e:
            self.error_occurred.emit(f"Threading error: {str(e)}")
    
    def export_to_excel(self):
        """Export results to Excel (reuse existing implementation)"""
        try:
            # Import the existing Excel export functionality
            from email_processor import ProcessingThread
            
            # Create a temporary single-threaded processor for Excel export
            temp_processor = ProcessingThread([], self.output_path)
            temp_processor.results = self.results
            temp_processor.export_to_excel()
            
        except Exception as e:
            self.error_occurred.emit(f"Excel export error: {str(e)}")


class ThreadingConfiguration:
    """Configuration class for threading options"""
    
    @staticmethod
    def get_optimal_thread_count() -> int:
        """Calculate optimal thread count based on system resources"""
        cpu_count = os.cpu_count()
        
        # Conservative approach: limit to 4 threads max
        # Each thread uses ~100MB for OCR models
        optimal_count = min(4, cpu_count)
        
        # On systems with limited cores, use fewer threads
        if cpu_count <= 2:
            optimal_count = 1
        elif cpu_count <= 4:
            optimal_count = 2
        
        return optimal_count
    
    @staticmethod
    def estimate_memory_usage(thread_count: int) -> int:
        """Estimate additional memory usage in MB"""
        base_memory = 50  # Base application memory
        ocr_memory_per_thread = 100  # OCR models per thread
        return base_memory + (thread_count * ocr_memory_per_thread)
    
    @staticmethod
    def should_use_threading(file_count: int) -> bool:
        """Determine if threading is beneficial for given file count"""
        # Threading overhead isn't worth it for small batches
        return file_count >= 5


# Example usage and testing
if __name__ == "__main__":
    # This would be integrated into the main GUI application
    print("Threaded Email Processor Example")
    print(f"Optimal thread count: {ThreadingConfiguration.get_optimal_thread_count()}")
    print(f"Estimated memory usage: {ThreadingConfiguration.estimate_memory_usage(4)}MB")
    
    # Example file list (would come from GUI)
    example_files = ["email1.eml", "email2.eml", "email3.eml"]
    
    if ThreadingConfiguration.should_use_threading(len(example_files)):
        print("Threading recommended for this batch size")
    else:
        print("Single-threaded processing recommended for this batch size")
