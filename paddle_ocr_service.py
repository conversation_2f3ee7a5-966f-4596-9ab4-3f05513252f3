"""
PaddleOCR-based OCR Service for Email Batch Processor
Provides robust OCR functionality with English and Chinese support
"""

import io
import logging
import numpy as np
from typing import List, Optional
from dataclasses import dataclass

# Configure logging to reduce PaddleOCR verbosity
logging.getLogger('ppocr').setLevel(logging.WARNING)
logging.getLogger('paddle').setLevel(logging.WARNING)


# Module logger for this service (use debug level to avoid console noise in production)
logger = logging.getLogger(__name__)

# Optional imports (performed lazily)
try:
    from PIL import Image
    _PIL_AVAILABLE = True
except ImportError:
    _PIL_AVAILABLE = False

try:
    import fitz  # PyMuPDF
    _PYMUPDF_AVAILABLE = True
except ImportError:
    _PYMUPDF_AVAILABLE = False


@dataclass
class OcrResult:
    """OCR result container"""
    text: str
    confidence: float = 0.0
    success: bool = True
    error: Optional[str] = None


class PaddleOcrService:
    """
    PaddleOCR-based OCR service with English and Chinese support
    Provides a clean interface for image and PDF text extraction
    """

    def __init__(self):
        self._ocr = None
        self._init_error: Optional[str] = None
        self._status = "initializing"
        self._initialize_ocr()

    def _initialize_ocr(self):
        """Initialize PaddleOCR with error handling"""
        try:
            # Import PaddleOCR
            from paddleocr import PaddleOCR

            # Check if we can import successfully
            self._paddle_ocr_class = PaddleOCR
            self._ocr = None  # Will be initialized on first use
            self._status = "available:paddleocr"

        except ImportError as e:
            self._init_error = f"PaddleOCR not installed: {str(e)}"
            self._status = "unavailable:missing_paddleocr"

        except Exception as e:
            self._init_error = f"PaddleOCR import failed: {str(e)}"
            self._status = "unavailable:import_failed"

    def _ensure_ocr_initialized(self):
        """Ensure OCR is initialized (lazy initialization)"""
        if self._ocr is None and hasattr(self, '_paddle_ocr_class'):
            try:
                # Initialize PaddleOCR with PP-OCRv5 models for best performance
                # Using Chinese+English support for maximum compatibility
                # Configure parameters to handle large images properly
                ocr_params = {
                    'lang': 'ch',
                    'use_angle_cls': True,  # Enable angle classification for better accuracy
                    'show_log': False,  # Reduce verbosity
                }

                # Try to initialize with parameters that handle large images better
                try:
                    self._ocr = self._paddle_ocr_class(**ocr_params)
                    logger.debug("PaddleOCR initialized with PP-OCRv5 models and optimized parameters")
                except Exception as param_error:
                    # If parameterized initialization fails, try simple initialization
                    logger.debug(f"Parameterized initialization failed: {param_error}")
                    logger.debug("Trying simple initialization...")
                    self._ocr = self._paddle_ocr_class(lang='ch')
                    logger.debug("PaddleOCR initialized with basic configuration")

            except Exception as e:
                # If initialization fails, try fallback configurations
                error_msg = str(e)
                if "already been initialized" in error_msg or "Unknown argument" in error_msg:
                    # Try simpler initialization
                    try:
                        self._ocr = self._paddle_ocr_class(lang='ch')
                        logger.debug("PaddleOCR initialized with fallback configuration")
                    except Exception as fallback_error:
                        self._init_error = f"PaddleOCR initialization failed: {fallback_error}"
                        self._status = "unavailable:init_failed"
                        raise RuntimeError(self._init_error)
                else:
                    self._init_error = f"PaddleOCR initialization failed: {error_msg}"
                    self._status = "unavailable:init_failed"
                    raise RuntimeError(self._init_error)

    @property
    def status(self) -> str:
        """Get current OCR service status"""
        return self._status

    def is_available(self) -> bool:
        """Check if OCR service is available"""
        return (self._ocr is not None or hasattr(self, '_paddle_ocr_class')) and _PIL_AVAILABLE

    def _normalize_paddle_result(self, result) -> OcrResult:
        """
        Normalize PaddleOCR result format
        Handles both legacy and modern PaddleOCR v3.2.0+ formats
        """
        if not result:
            return OcrResult(text="", confidence=0.0)

        texts = []
        confidences = []

        try:
            # Handle modern PaddleOCR v3.2.0+ format (dictionary with rec_texts and rec_scores)
            if isinstance(result, list) and len(result) > 0:
                first_item = result[0]

                # Modern format: [{'rec_texts': [...], 'rec_scores': [...], ...}]
                if isinstance(first_item, dict) and 'rec_texts' in first_item and 'rec_scores' in first_item:
                    rec_texts = first_item['rec_texts']
                    rec_scores = first_item['rec_scores']

                    for i, text in enumerate(rec_texts):
                        if text and text.strip():
                            texts.append(text.strip())
                            if i < len(rec_scores):
                                confidences.append(float(rec_scores[i]))
                            else:
                                confidences.append(0.0)

                # Legacy format: [[[bbox], (text, confidence)], ...]
                elif isinstance(first_item, list):
                    # If result is nested (common in older versions)
                    if len(first_item) > 0 and isinstance(first_item[0], list):
                        result = first_item  # Unwrap one level

                    # Process each detected text line
                    for line in result:
                        if isinstance(line, list) and len(line) >= 2:
                            # Extract text and confidence: [bbox, (text, confidence)]
                            text_info = line[1]
                            if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                                text, confidence = text_info[0], text_info[1]
                                if text and text.strip():
                                    texts.append(text.strip())
                                    confidences.append(float(confidence))
                        elif isinstance(line, dict):
                            # Handle dictionary format (alternative format)
                            if 'text' in line and 'confidence' in line:
                                text = line['text'].strip()
                                if text:
                                    texts.append(text)
                                    confidences.append(float(line['confidence']))

            # Combine all text with proper spacing
            combined_text = " ".join(texts)
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0

            return OcrResult(
                text=combined_text,
                confidence=avg_confidence,
                success=True
            )

        except Exception as e:
            return OcrResult(
                text="",
                confidence=0.0,
                success=False,
                error=f"Result parsing error: {str(e)}"
            )

    def _resize_image_if_needed(self, image: Image.Image, max_side_limit: int = 4000) -> Image.Image:
        """
        Resize image if any side exceeds the max_side_limit

        Args:
            image: PIL Image object
            max_side_limit: Maximum allowed dimension for any side

        Returns:
            Resized PIL Image object if resizing was needed, otherwise original image
        """
        width, height = image.size

        # Check if resizing is needed
        if width <= max_side_limit and height <= max_side_limit:
            return image

        # Calculate the scaling factor to fit within limits
        scale_factor = min(max_side_limit / width, max_side_limit / height)

        # Calculate new dimensions
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)

        logger.debug(f"Resizing image from ({width}x{height}) to ({new_width}x{new_height}) to fit within max_side_limit of {max_side_limit}")

        # Resize using high-quality resampling
        resized_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        return resized_image

    def extract_text_from_image(self, image_bytes: bytes) -> str:
        """
        Extract text from image bytes

        Args:
            image_bytes: Raw image data

        Returns:
            Extracted text string

        Raises:
            RuntimeError: If OCR service is not available or processing fails
        """
        if not self.is_available():
            raise RuntimeError(f"OCR service not available: {self._init_error or 'Unknown error'}")

        if not _PIL_AVAILABLE:
            raise RuntimeError("PIL (Pillow) is required for image processing")

        try:
            # Ensure OCR is initialized
            self._ensure_ocr_initialized()

            # Convert bytes to PIL Image
            image = Image.open(io.BytesIO(image_bytes))

            # Convert to RGB if necessary (PaddleOCR works best with RGB)
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Resize image if it exceeds the max_side_limit to prevent PaddleOCR crashes
            image = self._resize_image_if_needed(image, max_side_limit=4000)

            # Convert PIL Image to numpy array (required by PaddleOCR v3.2.0+)
            image_array = np.array(image)

            # Perform OCR using the modern predict API
            # The predict method is the recommended approach in PaddleOCR v3.2.0+
            try:
                result = self._ocr.predict(image_array)
            except AttributeError:
                # Fallback to ocr method if predict is not available
                import warnings
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore", DeprecationWarning)
                    result = self._ocr.ocr(image_array)
            except Exception as api_error:
                # If predict fails, try the legacy ocr method
                try:
                    import warnings
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore", DeprecationWarning)
                        result = self._ocr.ocr(image_array)
                except Exception:
                    raise RuntimeError(f"OCR API call failed: {api_error}")

            # Normalize result
            ocr_result = self._normalize_paddle_result(result)

            if not ocr_result.success:
                raise RuntimeError(ocr_result.error or "OCR processing failed")

            return ocr_result.text

        except Exception as e:
            error_msg = str(e)
            # Provide more informative error messages
            if "unexpected keyword argument" in error_msg:
                raise RuntimeError(f"PaddleOCR API compatibility issue: {error_msg}")
            elif "not supported input data type" in error_msg:
                raise RuntimeError(f"Image format not supported by PaddleOCR: {error_msg}")
            else:
                raise RuntimeError(f"Image OCR failed: {error_msg}")

    def extract_text_from_pdf(self, pdf_bytes: bytes) -> str:
        """
        Extract text from PDF bytes using OCR

        Args:
            pdf_bytes: Raw PDF data

        Returns:
            Extracted text string

        Raises:
            RuntimeError: If OCR service is not available or processing fails
        """
        if not self.is_available():
            raise RuntimeError(f"OCR service not available: {self._init_error or 'Unknown error'}")

        if not _PYMUPDF_AVAILABLE:
            raise RuntimeError("PyMuPDF is required for PDF processing")

        doc = None
        texts = []

        try:
            # Open PDF document
            doc = fitz.open(stream=pdf_bytes, filetype='pdf')

            for page_num in range(len(doc)):
                page = doc.load_page(page_num)

                # First try to extract native text
                native_text = page.get_text().strip()
                if native_text:
                    texts.append(native_text)
                    continue

                # If no native text, use OCR
                # Convert page to image with appropriate scaling
                # Use 1.5x scale instead of 2x to reduce memory usage while maintaining quality
                pix = page.get_pixmap(matrix=fitz.Matrix(1.5, 1.5))
                img_data = pix.tobytes("png")

                # Extract text using OCR (image resizing is handled in extract_text_from_image)
                try:
                    ocr_text = self.extract_text_from_image(img_data)
                    if ocr_text:
                        texts.append(ocr_text)
                except Exception as e:
                    # Log OCR failure but continue with other pages
                    logger.debug(f"OCR failed for page {page_num + 1}: {e}")
                    continue

            return "\n\n".join(texts)

        except Exception as e:
            raise RuntimeError(f"PDF OCR failed: {str(e)}")

        finally:
            if doc:
                doc.close()

    def get_info(self) -> dict:
        """Get OCR service information"""
        return {
            'service': 'PaddleOCR v3.2.0',
            'model': 'PP-OCRv5 (Latest)',
            'status': self.status,
            'available': self.is_available(),
            'languages': ['English', 'Chinese'],
            'features': [
                'PP-OCRv5 Text Detection',
                'PP-OCRv5 Text Recognition',
                'Document Orientation Classification',
                'Multi-language Support',
                'CPU Optimized Processing'
            ],
            'processing': 'CPU-only (GPU not required)',
            'error': self._init_error
        }


# Backward compatibility alias
OcrService = PaddleOcrService
