# Email Batch Processor

A comprehensive PyQt6 application for batch processing .eml email files with support for Chinese character encoding. This tool extracts detailed information from email files and exports the data to Excel format.

## Features

### Core Functionality
- **File Selection**: Select individual .eml files or entire directories
- **Comprehensive Data Extraction**: Extracts all email metadata and content
- **Excel Export**: Generates detailed Excel reports with proper formatting
- **Chinese Character Support**: Handles Chinese email content with proper encoding detection
- **Progress Tracking**: Real-time progress indication during batch processing
- **Error Handling**: Robust error handling for corrupted or malformed files

### Extracted Information
- Original filename and file path
- Email subject/title
- Email body content (text and HTML)
- Send/receive timestamps
- Sender information (email, display name)
- Recipient information (To, CC, BCC fields)
- Email headers (Message-ID, Reply-To, Priority, Importance)
- Content type and encoding information
- Attachment details (names, sizes, count)
- **NEW: Archive file contents** (ZIP, RAR, 7Z, TAR file listings)
- **NEW: OCR extracted text** (from images and PDFs)
- **NEW: Document content** (from Word, Excel, PowerPoint files)
- **NEW: Comprehensive processing status** and timing information
- Processing status and error information

## Requirements

- Python 3.7 or higher
- PyQt6 >= 6.5.0
- openpyxl >= 3.1.0
- chardet >= 5.0.0
- **NEW: rarfile >= 4.0** (for RAR file processing)
- **NEW: py7zr >= 0.20.0** (for 7Z file processing)
- **NEW: paddlepaddle >= 2.5.0** (PaddlePaddle framework)
- **NEW: paddleocr >= 2.7.0** (PaddleOCR for text recognition)
- **NEW: Pillow >= 10.0.0** (for image processing)
- **NEW: PyMuPDF >= 1.23.0** (for PDF processing)
- **NEW: python-docx >= 1.1.0** (for Word document processing)
- **NEW: python-pptx >= 0.6.23** (for PowerPoint processing)
- **NEW: xlrd >= 2.0.1** (for Excel file processing)

## Installation

Recommended: create and use a virtual environment, then install dependencies from requirements.txt.

Windows (PowerShell):

```bash
python -m venv .venv
.\.venv\Scripts\Activate.ps1
python -m pip install --upgrade pip
pip install -r requirements.txt
```

macOS/Linux (bash/zsh):

```bash
python3 -m venv .venv
source .venv/bin/activate
python -m pip install --upgrade pip
pip install -r requirements.txt
```

## Usage

### Running the Application
```bash
python email_processor.py
```
### Logging Configuration (optional)

By default, the application is production-quiet (debug logs suppressed). To enable detailed logs when troubleshooting, choose one of the following:

- Option A — Temporary change in the entry point (simplest): add near the top of your entry script (e.g., `email_processor.py`):

```python
import logging
logging.basicConfig(level=logging.DEBUG, format="%(asctime)s %(levelname)s %(name)s: %(message)s")
```

  - For production, use a quieter level:

```python
logging.basicConfig(level=logging.INFO)  # or logging.WARNING
```

  - Third‑party libraries are already tuned to be quiet, but you may explicitly do:

```python
for name in ("ppocr", "paddle"):
    logging.getLogger(name).setLevel(logging.WARNING)
```

- Option B — Without modifying the app code (wrapper script): create `run_debug.py` next to this README with:

```python
import logging, runpy
logging.basicConfig(level=logging.DEBUG, format="%(asctime)s %(levelname)s %(name)s: %(message)s")
runpy.run_path("email_processor.py", run_name="__main__")
```

Then run:

```bash
python run_debug.py
```


### Using the GUI

1. **Select Files**:
   - Click "Select .eml Files" to choose individual email files
   - Click "Select Folder" to process all .eml files in a directory
   - Use "Clear Selection" to reset your selection

2. **Set Output Location**:
   - Specify where to save the Excel report
   - Default filename includes timestamp for uniqueness

3. **Start Processing**:
   - Click "Start Processing" to begin batch processing
   - Monitor progress through the progress bar and status log
   - Use "Stop Processing" to cancel if needed

4. **Review Results**:
   - Check the processing log for any errors
   - Open the generated Excel file to review extracted data

### Troubleshooting and Debugging

For detailed troubleshooting and debugging of OCR processing runs, use the debug script:

```bash
python run_debug.py
```

**When to use `run_debug.py`:**
- OCR processing is failing or producing unexpected results
- Need detailed logs to diagnose attachment processing issues
- Investigating performance problems or processing errors
- Debugging encoding or character recognition problems

The debug script enables comprehensive logging that shows detailed information about each processing step, OCR operations, and error conditions that are normally suppressed in production mode.
## Production Runbook

- Run in a virtual environment
  - See Installation for creating/activating a venv. Ensure you run `pip install -r requirements.txt` inside the activated venv before launching the app.

- Disabling OCR (if not needed)
  - If you don’t need OCR, simply do not install `paddlepaddle` and `paddleocr` in your venv. The application will continue to work with OCR marked as unavailable, leaving OCR fields empty.
  - In an existing environment you can disable OCR by uninstalling these packages:

```bash
pip uninstall -y paddleocr paddlepaddle
```

- Basic troubleshooting (production)
  - Missing modules (e.g., PyQt6): install dependencies in the active venv with `pip install -r requirements.txt`.
  - Permission errors when saving Excel: pick a writable folder or run with appropriate permissions.
  - OCR issues (slow/inaccurate/failing): enable debug logs (see Logging Configuration), verify OCR dependencies are installed if you intend to use OCR, or disable OCR for speed.
  - Garbled text: ensure input .eml files are valid; encoding detection is automatic, but corrupted files may not parse correctly.

- Performance considerations for large batches
  - Prefer SSD/local disk for input and output; avoid network drives for best throughput.
  - Split very large directories into smaller batches (e.g., 1–2k emails at a time).
  - OCR is the most expensive step; disable it when not needed to maximize throughput.
  - Extremely large scanned PDFs may be slow; process them separately or without OCR.
  - Close other apps to free CPU/RAM during long runs.



### Excel Output Format

The generated Excel file contains the following columns:

| Column | Description |
|--------|-------------|
| Original Filename | Name of the .eml file |
| File Path | Full path to the source file |
| Subject | Email subject line |
| From Email | Sender's email address |
| From Name | Sender's display name |
| From Full | Complete sender information |
| To Emails | Recipient email addresses (semicolon-separated) |
| To Names | Recipient display names |
| To Full | Complete recipient information |
| CC Emails | CC recipient email addresses |
| CC Names | CC recipient display names |
| CC Full | Complete CC information |
| BCC Emails | BCC recipient email addresses |
| BCC Names | BCC recipient display names |
| BCC Full | Complete BCC information |
| Date | Email timestamp |
| Message ID | Unique message identifier |
| Reply To | Reply-to address |
| Priority | Email priority level |
| Importance | Email importance level |
| Content Type | MIME content type |
| Body Content | Email body text |
| Attachments Count | Number of attachments |
| Attachment Names | Names of all attachments |
| Attachment Sizes | Sizes of all attachments |
| Total Attachments Size | Combined size of all attachments |
| **Archive File List** | **Files contained in archive attachments** |
| **Archive File Count** | **Number of files in archives** |
| **Archive Total Size** | **Uncompressed size of archive contents** |
| **OCR Extracted Text** | **Text extracted from images and PDFs** |
| **Document Content** | **Text content from Office documents** |
| **Attachment Processing Status** | **Processing status for each attachment** |
| **Attachment Processing Time** | **Time taken for attachment processing** |
| Encoding Detected | Character encoding used |
| Has HTML | Whether email contains HTML content |
| Processing Error | Any errors encountered during processing |

### Enhanced Processing Features

#### Intelligent Archive Processing
- **Smart Format Detection:** Automatically detects correct archive format regardless of file extension
- **Multi-Format Fallback:** If primary format fails, tries alternative formats (ZIP → RAR → 7Z → TAR)
- **Enhanced Error Reporting:** Detailed status showing which formats were attempted and results
- **Misnamed File Handling:** Successfully processes files with incorrect extensions (e.g., .rar files that are actually ZIP)

#### Advanced Email Content Processing
- **Unicode Escape Decoding:** Automatically converts Unicode sequences (\\u60a8 → 您) to readable characters
- **HTML Content Cleaning:** Removes HTML tags while preserving content structure and readability
- **HTML Entity Processing:** Converts HTML entities (&nbsp;, &amp;, &lt;, &gt;) to readable text
- **Content Normalization:** Cleans excessive whitespace and normalizes line breaks for Excel compatibility
- **Mixed Content Handling:** Processes emails with both HTML markup and Unicode escape sequences

### OCR Architecture
- **PP-OCRv5 Integration:** Powered by the latest PP-OCRv5 models for state-of-the-art text recognition
- **Advanced Text Detection:** Superior text detection algorithms handling complex layouts and orientations
- **Multi-language Excellence:** Outstanding English and Chinese text recognition capabilities
- **CPU Optimized Processing:** Efficient OCR processing without requiring GPU hardware
- **Robust PDF Processing:** Native text extraction with intelligent OCR fallback for scanned content
- **Modern API Integration:** Uses PaddleOCR v3.2.0 with the latest `predict()` API for optimal performance
- **Production Ready:** Enterprise-grade OCR solution with proven reliability and active development
- **Graceful Degradation:** Application continues processing other content if OCR is unavailable


## Technical Details

### Character Encoding Support
The application automatically detects and handles various character encodings commonly used in Chinese emails:
- UTF-8
- GB2312
- GBK
- Big5
- ISO-8859-1

### Email Parsing
- Uses Python's built-in `email` library for robust email parsing
- Handles both single-part and multipart messages
- Extracts text and HTML content
- Processes encoded headers and content
- Identifies and catalogs attachments

### Error Handling
- Graceful handling of corrupted or malformed .eml files
- Detailed error reporting in the Excel output
- Continues processing remaining files even if some fail
- User-friendly error messages in the GUI

## Troubleshooting

### Common Issues

- Environment/setup: See “Production Runbook” (venv setup, installing dependencies, permission errors).
- OCR issues or slow performance: See “Production Runbook” (enable debug logs, disable OCR if not needed, large-batch guidance).
- Garbled or incorrect text: See “Production Runbook”. If it persists, verify the .eml files are valid and consider sharing sanitized headers for diagnosis.
- PyQt stylesheet warnings: These were resolved in recent versions. If you still see “Unknown property transform”, update to the latest code.

### Performance Tips
See the “Production Runbook” section for performance considerations and guidance on running large batches efficiently.

## License

This project is provided as-is for educational and practical use. Feel free to modify and distribute according to your needs.

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the processing log for specific error messages
3. Ensure all dependencies are properly installed
4. Verify that your .eml files are valid email files
