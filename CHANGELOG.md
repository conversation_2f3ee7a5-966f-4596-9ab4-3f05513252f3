# Changelog

All notable changes to this project will be documented in this file.

The format is based on Keep a Changelog, and this project adheres to Semantic Versioning where practical.

## [Unreleased]

- No unreleased changes at this time

## [1.0.0] - 2025-09-16

### Added
- PaddleOCR (PP-OCRv5) integration with Chinese + English support via `PaddleOcrService`
- Intelligent archive processing with smart format detection and multi-format fallback (ZIP, RAR, 7Z, TAR)
- OCR for images and PDFs with lazy initialization and graceful degradation when OCR is unavailable
- Office document text extraction (DOCX, PPTX, XLSX, and legacy XLS with best-effort handling)
- Email body processing improvements: Unicode escape decoding, HTML cleaning, HTML entity decoding, whitespace normalization
- Detailed Excel export with sanitized values to prevent XML corruption and formula injection
- Modernized UI stylesheet and utilities in `modern_styles.py`

### Changed
- Pre-validated and resized large images prior to OCR to respect PaddleOCR max_side_limit, using high-quality LANCZOS resampling
- PDF processing uses more conservative scaling (1.5x) to reduce memory usage while maintaining quality
- Replaced console `print()` statements in the OCR service with `logging`-based debug messages to avoid noisy console output in production
- Suppressed third-party Paddle/PPOCR logs to WARNING to reduce verbosity

### Fixed
- Resolved crashes when processing images larger than 4000px by adding a robust pre-resize step (see PaddleOCR max_side_limit fix)
- Improved handling of misnamed archives by trying multiple formats with clear status reporting
- Eliminated PyQt stylesheet warnings by using only supported properties

### Removed
- Test files: `test_all_emails.py`, `test_excel_export.py`, `test_large_image_fix.py`
- Sample test data directory contents under `test_emails/`
- Debug/console prints in `paddle_ocr_service.py`

### Notes on Dependencies
- Current runtime dependencies are listed in `requirements.txt` and include OCR-related packages (`paddlepaddle`, `paddleocr`, `Pillow`, `PyMuPDF`) and document/archiving libraries.
- Removed unused: `tqdm`, `python-magic` were not referenced in code and have been removed from the runtime environment and `requirements.txt`.

### Documentation Consolidation
- Content previously split across README and fix summaries has been reflected here as features/changes (notably the PaddleOCR setup and the large image fix summary). For full setup guidance, see `OCR_SETUP_GUIDE.md`.

### Documentation
- README: removed reference to `install_dependencies.py`; installation now uses virtual environments with `pip install -r requirements.txt`.
- README: added a Logging Configuration section showing how to enable DEBUG logs or keep production-quiet logging.
- README: added a Production Runbook with venv usage, optional OCR disabling, basic troubleshooting, and performance considerations for large batches.
- Added `run_debug.py` convenience script to enable DEBUG logging and run the application entry point.


[1.0.0]: https://example.com/releases/1.0.0

